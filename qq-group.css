* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
/* 在文件开头的通用样式中添加 */
.menu-toggle {
    display: none; /* 默认隐藏菜单按钮 */
} 
:root {
    --primary-color: #12b7f5;
    --secondary-color: #00a4e4;
    --accent-color: #ffd700;
    --background-dark: #1a1a2e;
    --background-light: #16213e;
    --text-color: #fff;
}

/* 星空背景 */
.stars, .twinkling {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.stars {
    background: #000 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQBAMAAABykSv/AAAAG1BMVEUAAAD///8fHx8/Pz9fX1+fn5+/v7/f39////9Tp+HfAAAACXBIWXMAAAsTAAALEwEAmpwYAAAF0WlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNy4yLWMwMDAgNzkuMWI2NWE3OWI0LCAyMDIyLzA2LzEzLTIyOjAxOjAxICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdEV2dD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlRXZlbnQjIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnBob3Rvc2hvcD0iaHR0cDovL25zLmFkb2JlLmNvbS9waG90b3Nob3AvMS4wLyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjQuMCAoTWFjaW50b3NoKSIgeG1wOkNyZWF0ZURhdGU9IjIwMjQtMDItMTVUMTU6NDc6NDcrMDg6MDAiIHhtcDpNZXRhZGF0YURhdGU9IjIwMjQtMDItMTVUMTU6NDc6NDcrMDg6MDAiIHhtcDpNb2RpZnlEYXRlPSIyMDI0LTAyLTE1VDE1OjQ3OjQ3KzA4OjAwIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgeG1wTU06RG9jdW1lbnRJRD0iYWRvYmU6ZG9jaWQ6cGhvdG9zaG9wOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgeG1wTU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgZGM6Zm9ybWF0PSJpbWFnZS9wbmciIHBob3Rvc2hvcDpDb2xvck1vZGU9IjMiPiA8eG1wTU06SGlzdG9yeT4gPHJkZjpTZXE+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjcmVhdGVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgc3RFdnQ6d2hlbj0iMjAyNC0wMi0xNVQxNTo0Nzo0NyswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDI0LjAgKE1hY2ludG9zaCkiLz4gPC9yZGY6U2VxPiA8L3htcE1NOkhpc3Rvcnk+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+') repeat top center;
    z-index: -2;
}

.twinkling {
    background: transparent url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQBAMAAABykSv/AAAAG1BMVEUAAAD///8fHx8/Pz9fX1+fn5+/v7/f39////9Tp+HfAAAACXBIWXMAAAsTAAALEwEAmpwYAAAF0WlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNy4yLWMwMDAgNzkuMWI2NWE3OWI0LCAyMDIyLzA2LzEzLTIyOjAxOjAxICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdEV2dD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlRXZlbnQjIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnBob3Rvc2hvcD0iaHR0cDovL25zLmFkb2JlLmNvbS9waG90b3Nob3AvMS4wLyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjQuMCAoTWFjaW50b3NoKSIgeG1wOkNyZWF0ZURhdGU9IjIwMjQtMDItMTVUMTU6NDc6NDcrMDg6MDAiIHhtcDpNZXRhZGF0YURhdGU9IjIwMjQtMDItMTVUMTU6NDc6NDcrMDg6MDAiIHhtcDpNb2RpZnlEYXRlPSIyMDI0LTAyLTE1VDE1OjQ3OjQ3KzA4OjAwIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgeG1wTU06RG9jdW1lbnRJRD0iYWRvYmU6ZG9jaWQ6cGhvdG9zaG9wOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgeG1wTU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgZGM6Zm9ybWF0PSJpbWFnZS9wbmciIHBob3Rvc2hvcDpDb2xvck1vZGU9IjMiPiA8eG1wTU06SGlzdG9yeT4gPHJkZjpTZXE+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjcmVhdGVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjQ5YzFiOWY5LTU0ZDgtNDI0ZC1hMjY5LTY5ZjM5ZjM5ZjM5ZiIgc3RFdnQ6d2hlbj0iMjAyNC0wMi0xNVQxNTo0Nzo0NyswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDI0LjAgKE1hY2ludG9zaCkiLz4gPC9yZGY6U2VxPiA8L3htcE1NOkhpc3Rvcnk+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+') repeat top center;
    z-index: -1;
    animation: move-twink-back 200s linear infinite;
}

@keyframes move-twink-back {
    from {background-position: 0 0;}
    to {background-position: -10000px 5000px;}
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-light) 100%);
    color: var(--text-color);
    min-height: 100vh;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.nav-brand i {
    font-size: 1.8rem;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a.active {
    color: var(--primary-color);
}

/* 下拉菜单样式 */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.dropdown-toggle:hover {
    color: var(--primary-color);
}

.dropdown-toggle i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-toggle i {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 0.8rem 1.2rem;
    color: var(--text-color);
    text-decoration: none;
    font-size: 1rem;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.dropdown-menu a:hover {
    background: rgba(18, 183, 245, 0.1);
    color: var(--primary-color);
    padding-left: 1.5rem;
}

/* 英雄区域样式 */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 6rem 2rem 2rem;
    overflow: hidden;
}

.hero-content {
    text-align: center;
    z-index: 1;
}

/* 故障艺术效果 */
.glitch {
    font-size: 4rem;
    font-weight: bold;
    text-transform: uppercase;
    position: relative;
    text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff,
                 0.025em 0.04em 0 #fffc00;
    animation: glitch 725ms infinite;
}

.glitch span {
    position: absolute;
    top: 0;
    left: 0;
}

@keyframes glitch {
    0% {
        text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff,
                     0.025em 0.04em 0 #fffc00;
    }
    15% {
        text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff,
                     0.025em 0.04em 0 #fffc00;
    }
    16% {
        text-shadow: -0.05em -0.025em 0 #00fffc, 0.025em 0.035em 0 #fc00ff,
                     -0.05em -0.05em 0 #fffc00;
    }
    49% {
        text-shadow: -0.05em -0.025em 0 #00fffc, 0.025em 0.035em 0 #fc00ff,
                     -0.05em -0.05em 0 #fffc00;
    }
    50% {
        text-shadow: 0.05em 0.035em 0 #00fffc, 0.03em 0 0 #fc00ff,
                     0 -0.04em 0 #fffc00;
    }
    99% {
        text-shadow: 0.05em 0.035em 0 #00fffc, 0.03em 0 0 #fc00ff,
                     0 -0.04em 0 #fffc00;
    }
    100% {
        text-shadow: -0.05em 0 0 #00fffc, -0.025em -0.04em 0 #fc00ff,
                     -0.04em -0.025em 0 #fffc00;
    }
}

.subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.8);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Hero Actions */
.hero-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 30px;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.cta-button.primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--text-color);
}

.cta-button.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(18, 183, 245, 0.4);
}

.cta-button.secondary {
    background: transparent;
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.cta-button.secondary:hover {
    background: var(--accent-color);
    color: var(--background-dark);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
}

/* 特性部分样式 */
.features-section {
    padding: 6rem 2rem;
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* 成员部分样式 */
.members-section {
    padding: 6rem 2rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.2);
}

.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.member-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.member-card:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.15);
}

.member-avatar {
    width: 100px;
    height: 100px;
    margin: 0 auto 1rem;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.member-avatar i {
    font-size: 3rem;
    color: var(--text-color);
}

/* 群规部分样式 */
.rules-section {
    padding: 6rem 2rem;
    text-align: center;
}

.rules-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.rule-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.rule-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(10px);
}

.rule-card i {
    font-size: 2rem;
    color: var(--primary-color);
}

/* 加入部分样式 */
.join-section {
    padding: 6rem 2rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.2);
}

.join-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
    margin-top: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.qr-code {
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.qr-code img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 10px;
}

.qr-code:hover {
    transform: translateY(-5px);
}

.join-info {
    text-align: left;
}

.join-btn {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--text-color);
    border: none;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-size: 1.2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.join-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 20px rgba(18, 183, 245, 0.3);
}

/* 页脚样式 */
.footer {
    background: rgba(26, 26, 46, 0.95);
    padding: 3rem 2rem 1rem;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar {
        padding: 0.8rem 1rem;
    }

    .menu-toggle {
        display: block;
        position: fixed;
        top: 1rem;
        right: 1rem;
    }

    .nav-links {
        position: fixed;
        top: 0;
        right: -100%;
        width: 70%;
        height: 100vh;
        background: rgba(26, 26, 46, 0.95);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: right 0.3s ease;
        z-index: 1001;
        padding: 2rem;
    }

    .nav-links.active {
        right: 0;
    }

    .nav-links a {
        font-size: 1.2rem;
        padding: 1rem 2rem;
        width: 100%;
        text-align: center;
    }

    .hero-section {
        padding: 5rem 1rem 2rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .subtitle {
        font-size: 1.2rem;
        padding: 0 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 2rem;
        padding: 0 1rem;
    }

    .stat-item {
        width: 100%;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
    }

    .features-section {
        padding: 4rem 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .members-section {
        padding: 4rem 1rem;
    }

    .members-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }

    .member-card {
        padding: 1.5rem;
    }

    .member-avatar {
        width: 80px;
        height: 80px;
    }

    .member-avatar i {
        font-size: 2.5rem;
    }

    .rules-section {
        padding: 4rem 1rem;
    }

    .rules-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }

    .rule-card {
        padding: 1.5rem;
    }

    .join-section {
        padding: 4rem 1rem;
    }

    .join-container {
        flex-direction: column;
        gap: 2rem;
        padding: 0 1rem;
    }

    .qr-code {
        width: 180px;
        height: 180px;
    }

    .join-info {
        text-align: center;
    }

    .join-btn {
        margin: 1rem auto 0;
    }

    .footer {
        padding: 2rem 1rem 1rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .footer-links a {
        padding: 0.5rem 1rem;
    }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .stat-item {
        padding: 0.8rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .feature-card i {
        font-size: 2rem;
    }

    .member-avatar {
        width: 60px;
        height: 60px;
    }

    .member-avatar i {
        font-size: 2rem;
    }

    .rule-card {
        padding: 1rem;
    }

    .rule-card i {
        font-size: 1.5rem;
    }

    .qr-code {
        width: 150px;
        height: 150px;
    }

    .qr-code i {
        font-size: 4rem;
    }
}

/* 横屏模式适配 */
@media (max-height: 500px) and (orientation: landscape) {
    .hero-section {
        min-height: auto;
        padding: 6rem 1rem 2rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .stat-item {
        width: calc(33.33% - 1rem);
    }

    .nav-links {
        padding: 0.5rem;
    }

    .nav-links a {
        padding: 0.5rem 1rem;
    }
}

/* 移动端下拉菜单适配 */
@media (max-width: 768px) {
    .dropdown {
        width: 100%;
    }
    
    .dropdown-toggle {
        justify-content: center;
        padding: 1rem 2rem;
        width: 100%;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        background: rgba(0, 0, 0, 0.3);
        border: none;
        border-radius: 0;
        box-shadow: none;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    
    .dropdown:hover .dropdown-menu {
        max-height: 200px;
    }
    
    .dropdown-menu a {
        padding: 0.8rem 2rem;
        text-align: center;
    }
    
    .dropdown-menu a:hover {
        padding-left: 2rem;
    }
}

#newMenu{
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 18vh;
    /* background:rgb(142, 106, 177) */
}
ul{
    position: relative;
    transform: skewY(-15deg);
}

ul li{
    position: relative;
    list-style: none;
    width: 200px;
    background: #ff1;
    padding: 15px;
    z-index: var(--i);
    transition: 0.5s;
}
ul li:hover{
    background: #33a3ee;
    transform: translateX(-50px);
}

ul li::before
{
content: "";
position: absolute;
background: red;
width: 40px;
height: 100%;
top:0;
left: -40px;
transform: skewY(45deg);
transform-origin: right;
transition: 0.5s;
}
ul li:hover::before{
    background: #33a3ee;
}
ul li::after{
    content: "";
    position: absolute;
    width: 100%;
    height: 40px;
    top:-40px;
    left: 0;
    background: #2e3113;
    transform-origin: bottom;
    transform: skewX(45deg);
    transition: 0.5s;

}
ul li:hover::after{
background: #33a3ee;
}
ul li a{
    text-decoration: none;
    color: #999;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: 0.5s;
}
ul li:hover a{
 color: #fff;
}
/* QQ登录按钮样式 */
.qq-login-btn {
    display: inline-block;
    margin: 0 10px;
}

.qq-login-btn.large {
    margin: 15px 0;
}

.nav-links .qq-login-btn {
    margin-left: auto;
}

.footer-login {
    margin-top: 10px;
}

/* 用户信息区域样式 */
.user-info {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.user-avatar {
    width: 30px;    
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #eee;
}

.user-nickname {
    color: #fff;
    margin-right: 10px;
    font-size: 14px;
}

.logout-btn {
    background: transparent;
    border: 1px solid #fff;
    color: #fff;
    padding: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.logout-btn:hover {
    background: rgba(255,255,255,0.1);
}   