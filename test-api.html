<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ用户信息查询</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        .input-group {
            margin-bottom: 15px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            box-sizing: border-box;
        }
        button {
            background-color: #12B7F5;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 16px;
            border-radius: 4px;
        }
        button:hover {
            background-color: #0E9FD8;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: none;
        }
        #avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 10px auto;
            display: block;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>QQ用户信息查询</h1>
    
    <div class="input-group">
        <label for="accessToken">Access Token:</label>
        <input type="text" id="accessToken" placeholder="输入access_token" value="0649A816B2E310A714C32DCFC83441B4">
    </div>
    
    <div class="input-group">
        <label for="openId">OpenID:</label>
        <input type="text" id="openId" placeholder="输入openid" value="9FA7C9581E5F3E00C549D878C39CCEB2">
    </div>
    
    <div class="input-group">
        <label for="appId">QQ互联AppID:</label>
        <input type="text" id="appId" placeholder="输入你的QQ互联AppID">
    </div>
    
    <button onclick="getUserInfo()">获取用户信息</button>
    
    <div id="result">
        <h2>用户信息</h2>
        <img id="avatar" src="" alt="头像">
        <p><strong>昵称:</strong> <span id="nickname"></span></p>
        <p><strong>性别:</strong> <span id="gender"></span></p>
    </div>
    
    <p id="error" class="error"></p>

    <script>
        function getUserInfo() {
            const accessToken = document.getElementById('accessToken').value.trim();
            const openId = document.getElementById('openId').value.trim();
            const appId = document.getElementById('appId').value.trim();
            const errorElement = document.getElementById('error');
            
            // 验证输入
            if (!accessToken || !openId || !appId) {
                errorElement.textContent = '请填写所有字段！';
                return;
            }
            
            errorElement.textContent = '';
            
            // 构建API URL
            const apiUrl = `https://graph.qq.com/user/get_user_info?access_token=${accessToken}&oauth_consumer_key=${appId}&openid=${openId}`;
            
            // 显示加载状态
            const button = document.querySelector('button');
            button.disabled = true;
            button.textContent = '请求中...';
            
            // 调用API
            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    button.disabled = false;
                    button.textContent = '获取用户信息';
                    
                    if (data.ret === 0) {
                        // 成功获取数据
                        document.getElementById('nickname').textContent = data.nickname;
                        document.getElementById('gender').textContent = data.gender;
                        
                        // 使用最高质量的头像
                        const avatarUrl = data.figureurl_qq_2 || data.figureurl_2 || data.figureurl;
                        document.getElementById('avatar').src = avatarUrl;
                        
                        // 显示结果区域
                        document.getElementById('result').style.display = 'block';
                    } else {
                        errorElement.textContent = `错误: ${data.msg || '未知错误'} (代码: ${data.ret})`;
                    }
                })
                .catch(error => {
                    button.disabled = false;
                    button.textContent = '获取用户信息';
                    errorElement.textContent = `请求失败: ${error.message}`;
                });
        }
    </script>
</body>
</html>