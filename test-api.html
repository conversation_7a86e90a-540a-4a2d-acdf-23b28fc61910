<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ登录API测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #12b7f5, #1e90ff);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #12b7f5;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #12b7f5;
        }
        
        .btn {
            background: linear-gradient(45deg, #12b7f5, #1e90ff);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(18, 183, 245, 0.4);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #6c757d, #5a6268);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .result-container {
            margin-top: 20px;
        }
        
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .result-box.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .result-box.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .result-box.info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.online {
            background: #28a745;
        }
        
        .status-indicator.offline {
            background: #dc3545;
        }
        
        .server-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .info-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .info-card h4 {
            color: #12b7f5;
            margin-bottom: 10px;
        }
        
        .info-card p {
            color: #666;
            font-size: 14px;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #12b7f5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .user-preview {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: #f0f8ff;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #12b7f5;
        }
        
        .user-info h4 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .user-info p {
            color: #666;
            font-size: 14px;
            margin: 2px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 QQ登录API测试中心</h1>
            <p>测试后端代理服务器的各项功能</p>
        </div>
        
        <div class="content">
            <!-- 服务器状态 -->
            <div class="test-section">
                <h3><span id="serverStatus" class="status-indicator offline"></span>服务器状态</h3>
                <div class="server-info">
                    <div class="info-card">
                        <h4>服务器地址</h4>
                        <p id="serverUrl">http://localhost:3000</p>
                    </div>
                    <div class="info-card">
                        <h4>连接状态</h4>
                        <p id="connectionStatus">检查中...</p>
                    </div>
                    <div class="info-card">
                        <h4>最后检查</h4>
                        <p id="lastCheck">-</p>
                    </div>
                </div>
                <button class="btn" onclick="checkServerHealth()">检查服务器状态</button>
                <button class="btn secondary" onclick="getServerInfo()">获取服务器信息</button>
            </div>
            
            <!-- QQ用户信息API测试 -->
            <div class="test-section">
                <h3>🔍 QQ用户信息API测试</h3>
                <div class="form-group">
                    <label for="accessToken">Access Token:</label>
                    <input type="text" id="accessToken" placeholder="输入QQ登录获取的access_token">
                </div>
                <div class="form-group">
                    <label for="openId">Open ID:</label>
                    <input type="text" id="openId" placeholder="输入QQ用户的openid">
                </div>
                <button class="btn" onclick="testUserInfoAPI()">测试用户信息API</button>
                <button class="btn secondary" onclick="useCurrentUser()">使用当前登录用户</button>
                <button class="btn success" onclick="fillTestData()">填入测试数据</button>
            </div>
            
            <!-- 结果显示区域 -->
            <div class="result-container">
                <div id="resultArea"></div>
            </div>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://localhost:3000';
        
        // 页面加载时自动检查服务器状态
        document.addEventListener('DOMContentLoaded', function() {
            checkServerHealth();
            loadCurrentUserData();
        });
        
        // 检查服务器健康状态
        async function checkServerHealth() {
            const statusIndicator = document.getElementById('serverStatus');
            const connectionStatus = document.getElementById('connectionStatus');
            const lastCheck = document.getElementById('lastCheck');
            
            connectionStatus.textContent = '检查中...';
            statusIndicator.className = 'status-indicator offline';
            
            try {
                const response = await fetch(`${SERVER_URL}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    statusIndicator.className = 'status-indicator online';
                    connectionStatus.textContent = '在线';
                    showResult('success', '服务器健康检查', data);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusIndicator.className = 'status-indicator offline';
                connectionStatus.textContent = '离线';
                showResult('error', '服务器连接失败', {
                    error: error.message,
                    message: '请确保服务器已启动并运行在端口3000'
                });
            }
            
            lastCheck.textContent = new Date().toLocaleTimeString();
        }
        
        // 获取服务器信息
        async function getServerInfo() {
            try {
                const response = await fetch(`${SERVER_URL}/api/info`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('info', '服务器信息', data);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                showResult('error', '获取服务器信息失败', {
                    error: error.message
                });
            }
        }
        
        // 测试QQ用户信息API
        async function testUserInfoAPI() {
            const accessToken = document.getElementById('accessToken').value.trim();
            const openId = document.getElementById('openId').value.trim();
            
            if (!accessToken || !openId) {
                showResult('error', '参数错误', {
                    message: '请填入access_token和openid'
                });
                return;
            }
            
            const loadingBtn = event.target;
            const originalText = loadingBtn.innerHTML;
            loadingBtn.innerHTML = '<span class="loading"></span>请求中...';
            loadingBtn.disabled = true;
            
            try {
                const url = `${SERVER_URL}/api/qq/userinfo?access_token=${accessToken}&openid=${openId}`;
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                console.log('API响应:', data);
                
                if (response.ok && data.ret === 0) {
                    showResult('success', 'QQ用户信息获取成功', data);
                    showUserPreview(data);
                } else {
                    showResult('error', 'QQ API返回错误', data);
                }
            } catch (error) {
                showResult('error', '请求失败', {
                    error: error.message,
                    message: '请检查网络连接和服务器状态'
                });
            } finally {
                loadingBtn.innerHTML = originalText;
                loadingBtn.disabled = false;
            }
        }
        
        // 使用当前登录用户的数据
        function useCurrentUser() {
            const userInfo = localStorage.getItem('qqUserInfo');
            if (userInfo) {
                try {
                    const data = JSON.parse(userInfo);
                    document.getElementById('accessToken').value = data.accessToken || '';
                    document.getElementById('openId').value = data.openId || data.openid || '';
                    showResult('info', '已加载当前用户数据', {
                        message: '已从本地存储加载用户数据',
                        nickname: data.nickname,
                        loginMethod: data.loginMethod
                    });
                } catch (e) {
                    showResult('error', '解析用户数据失败', {
                        error: e.message
                    });
                }
            } else {
                showResult('error', '未找到用户数据', {
                    message: '请先进行QQ登录'
                });
            }
        }
        
        // 填入测试数据
        function fillTestData() {
            document.getElementById('accessToken').value = 'TEST_ACCESS_TOKEN';
            document.getElementById('openId').value = 'TEST_OPENID';
            showResult('info', '已填入测试数据', {
                message: '这些是无效的测试数据，用于测试API错误处理'
            });
        }
        
        // 加载当前用户数据到表单
        function loadCurrentUserData() {
            const userInfo = localStorage.getItem('qqUserInfo');
            if (userInfo) {
                try {
                    const data = JSON.parse(userInfo);
                    document.getElementById('accessToken').value = data.accessToken || '';
                    document.getElementById('openId').value = data.openId || data.openid || '';
                } catch (e) {
                    console.log('无法解析用户数据');
                }
            }
        }
        
        // 显示结果
        function showResult(type, title, data) {
            const resultArea = document.getElementById('resultArea');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultBox = document.createElement('div');
            resultBox.className = `result-box ${type}`;
            
            resultBox.innerHTML = `
                <h4>${title} <small style="opacity: 0.7;">[${timestamp}]</small></h4>
                <div class="json-display">${JSON.stringify(data, null, 2)}</div>
            `;
            
            resultArea.insertBefore(resultBox, resultArea.firstChild);
            
            // 限制显示的结果数量
            while (resultArea.children.length > 5) {
                resultArea.removeChild(resultArea.lastChild);
            }
        }
        
        // 显示用户预览
        function showUserPreview(data) {
            const resultArea = document.getElementById('resultArea');
            
            const previewBox = document.createElement('div');
            previewBox.className = 'result-box success';
            
            const avatar = data.figureurl_qq_2 || data.figureurl_qq_1 || data.figureurl_2 || data.figureurl_1 || '';
            
            previewBox.innerHTML = `
                <h4>用户信息预览</h4>
                <div class="user-preview">
                    <img class="user-avatar" src="${avatar}" alt="用户头像" onerror="this.style.display='none'">
                    <div class="user-info">
                        <h4>${data.nickname || '未知用户'}</h4>
                        <p>性别: ${data.gender || '未知'}</p>
                        <p>地区: ${data.province || '未知'} ${data.city || ''}</p>
                        <p>OpenID: ${(data.openid || '').substring(0, 10)}...</p>
                    </div>
                </div>
            `;
            
            resultArea.insertBefore(previewBox, resultArea.firstChild);
        }
    </script>
</body>
</html>
