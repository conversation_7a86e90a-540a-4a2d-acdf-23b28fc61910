<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .json-display {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #12b7f5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0ea5e9;
        }
        .user-preview {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            margin-top: 10px;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        .user-info {
            flex: 1;
        }
        .user-nickname {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        .user-details {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ登录调试页面</h1>
        
        <div class="section">
            <h3>当前登录状态</h3>
            <button onclick="checkLoginStatus()">检查登录状态</button>
            <button onclick="clearLoginInfo()">清除登录信息</button>
            <div id="loginStatus"></div>
        </div>
        
        <div class="section">
            <h3>用户信息预览</h3>
            <div id="userPreview"></div>
        </div>
        
        <div class="section">
            <h3>原始用户数据</h3>
            <div id="rawUserData" class="json-display">暂无数据</div>
        </div>
        
        <div class="section">
            <h3>头像测试</h3>
            <button onclick="testAvatars()">测试所有头像URL</button>
            <div id="avatarTest"></div>
        </div>
    </div>

    <script>
        function checkLoginStatus() {
            const userInfo = localStorage.getItem('qqUserInfo');
            const statusDiv = document.getElementById('loginStatus');
            const previewDiv = document.getElementById('userPreview');
            const rawDataDiv = document.getElementById('rawUserData');
            
            if (userInfo) {
                try {
                    const data = JSON.parse(userInfo);
                    
                    // 显示登录状态
                    statusDiv.innerHTML = '<span style="color: green;">✓ 已登录</span>';
                    
                    // 显示原始数据
                    rawDataDiv.textContent = JSON.stringify(data, null, 2);
                    
                    // 显示用户预览
                    displayUserPreview(data);
                    
                } catch (e) {
                    statusDiv.innerHTML = '<span style="color: red;">✗ 用户信息格式错误</span>';
                    rawDataDiv.textContent = '解析错误: ' + e.message;
                }
            } else {
                statusDiv.innerHTML = '<span style="color: orange;">? 未登录</span>';
                rawDataDiv.textContent = '暂无登录信息';
                previewDiv.innerHTML = '';
            }
        }
        
        function displayUserPreview(data) {
            const previewDiv = document.getElementById('userPreview');
            
            // 获取头像URL
            let avatar = '';
            if (data.figureurl_qq_2) {
                avatar = data.figureurl_qq_2;
            } else if (data.figureurl_qq_1) {
                avatar = data.figureurl_qq_1;
            } else if (data.figureurl_2) {
                avatar = data.figureurl_2;
            } else if (data.figureurl_1) {
                avatar = data.figureurl_1;
            } else if (data.figureurl) {
                avatar = data.figureurl;
            } else {
                const openid = data.openId || data.openid;
                if (openid) {
                    avatar = 'https://q.qlogo.cn/headimg_dl/' + openid + '/100';
                }
            }
            
            const nickname = data.nickname || data.nick || 'QQ用户';
            const openid = data.openId || data.openid || '未知';
            
            previewDiv.innerHTML = `
                <div class="user-preview">
                    <img class="user-avatar" src="${avatar}" alt="用户头像" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNEREREREQiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMTAgMzJDMTAgMjYuNDc3MSAxNC40NzcxIDIyIDE5IDIySDIxQzI1LjUyMjkgMjIgMzAgMjYuNDc3MSAzMCAzMlYzNEgxMFYzMloiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+'">
                    <div class="user-info">
                        <div class="user-nickname">${nickname}</div>
                        <div class="user-details">
                            OpenID: ${openid.substring(0, 10)}...<br>
                            性别: ${data.gender || '未知'}<br>
                            地区: ${data.province || '未知'} ${data.city || ''}<br>
                            登录方式: ${data.loginMethod || '未知'}
                        </div>
                    </div>
                </div>
                <p><strong>使用的头像URL:</strong> ${avatar}</p>
            `;
        }
        
        function clearLoginInfo() {
            localStorage.removeItem('qqUserInfo');
            localStorage.removeItem('returnUrl');
            alert('登录信息已清除');
            checkLoginStatus();
        }
        
        function testAvatars() {
            const userInfo = localStorage.getItem('qqUserInfo');
            const testDiv = document.getElementById('avatarTest');
            
            if (!userInfo) {
                testDiv.innerHTML = '<p style="color: red;">请先登录</p>';
                return;
            }
            
            try {
                const data = JSON.parse(userInfo);
                const openid = data.openId || data.openid;
                
                const avatarUrls = [
                    { name: 'figureurl_qq_2', url: data.figureurl_qq_2 },
                    { name: 'figureurl_qq_1', url: data.figureurl_qq_1 },
                    { name: 'figureurl_2', url: data.figureurl_2 },
                    { name: 'figureurl_1', url: data.figureurl_1 },
                    { name: 'figureurl', url: data.figureurl },
                    { name: 'QQ官方API', url: openid ? `https://q.qlogo.cn/headimg_dl/${openid}/100` : null }
                ];
                
                let html = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">';
                
                avatarUrls.forEach(item => {
                    if (item.url) {
                        html += `
                            <div style="text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <div style="font-weight: bold; margin-bottom: 10px;">${item.name}</div>
                                <img src="${item.url}" style="width: 60px; height: 60px; border-radius: 50%; border: 1px solid #ccc;" 
                                     onerror="this.style.border='2px solid red'; this.alt='加载失败'">
                                <div style="font-size: 10px; margin-top: 5px; word-break: break-all;">${item.url}</div>
                            </div>
                        `;
                    }
                });
                
                html += '</div>';
                testDiv.innerHTML = html;
                
            } catch (e) {
                testDiv.innerHTML = '<p style="color: red;">解析用户信息失败</p>';
            }
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });
    </script>
</body>
</html>
