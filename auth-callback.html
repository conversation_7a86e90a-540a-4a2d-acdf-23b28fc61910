<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>登录处理中...</title>
    <script type="text/javascript" src="https://connect.qq.com/qc_jssdk.js" charset="utf-8"></script>
    <script>
        // 重写QC.Login.check方法，避免本地端口检查
        QC.Login.check = function(params) {
            if (typeof params.cb === "function") {
                // 直接从URL中获取openid和access_token
                const hash = window.location.hash.substring(1);
                const params = new URLSearchParams(hash);
                const openId = params.get('openid');
                const accessToken = params.get('access_token');
                
                if (openId && accessToken) {
                    // 模拟成功回调
                    setTimeout(() => params.cb(true, openId, accessToken), 500);
                } else {
                    // 模拟失败回调
                    setTimeout(() => params.cb(false), 500);
                }
            }
        };

        // 页面加载后处理
        document.addEventListener('DOMContentLoaded', function() {
            // 获取用户信息
            function getUserInfo(accessToken, openId) {
                QC.api("get_user_info", {
                    accessToken: accessToken,
                    openid: openId
                })
                .success(function(response) {
                    // 存储用户信息到本地存储
                    localStorage.setItem('qqUserInfo', JSON.stringify({
                        ...response.data,
                        accessToken: accessToken,
                        openId: openId
                    }));
                    
                    // 跳转回原页面或首页
                    const returnUrl = localStorage.getItem('returnUrl') || 'index.html';
                    window.location.href = returnUrl;
                })
                .error(function(error) {
                    console.error("获取用户信息失败:", error);
                    alert("获取用户信息失败，请重试");
                    window.location.href = 'index.html';
                });
            }

            // 从URL中解析参数
            const hash = window.location.hash.substring(1);
            const params = new URLSearchParams(hash);
            const accessToken = params.get('access_token');
            const openId = params.get('openid');
            
            if (accessToken && openId) {
                // 有token和openid，直接获取用户信息
                getUserInfo(accessToken, openId);
            } else {
                // 没有有效参数，跳转回首页
                alert("登录参数缺失，请重试");
                window.location.href = 'index.html';
            }
        });
    </script>
</head>
<body>
    <div style="text-align: center; margin-top: 100px;">
        <h2>登录处理中，请稍候...</h2>
    </div>
</body>
</html>