<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>登录处理中...</title>
    <script type="text/javascript" src="https://connect.qq.com/qc_jssdk.js" charset="utf-8"></script>
    <script>
        // 重写QC.Login.check方法，避免本地端口检查
        QC.Login.check = function(params) {
            if (typeof params.cb === "function") {
                // 直接从URL中获取openid和access_token
                const hash = window.location.hash.substring(1);
                const params = new URLSearchParams(hash);
                const openId = params.get('openid');
                const accessToken = params.get('access_token');
                
                if (openId && accessToken) {
                    // 模拟成功回调
                    setTimeout(() => params.cb(true, openId, accessToken), 500);
                } else {
                    // 模拟失败回调
                    setTimeout(() => params.cb(false), 500);
                }
            }
        };

        // 更新状态文本
        function updateStatus(message) {
            const statusElement = document.getElementById('statusText');
            if (statusElement) {
                statusElement.textContent = message;
            }
            console.log("状态更新:", message);
        }

        // 页面加载后处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log("页面加载完成，开始处理登录回调");
            console.log("当前URL:", window.location.href);
            console.log("Hash:", window.location.hash);
            console.log("Search:", window.location.search);

            updateStatus("正在解析登录参数...");

            // 获取用户信息
            function getUserInfo(accessToken, openId) {
                console.log("开始获取用户信息，accessToken:", accessToken, "openId:", openId);

                // 直接使用HTTP请求获取用户信息
                getUserInfoByHttp(accessToken, openId);
            }

            // 通过HTTP请求获取用户信息
            function getUserInfoByHttp(accessToken, openId) {
                updateStatus("正在获取用户详细信息...");
                console.log("通过QQ API获取用户详细信息");
                console.log("accessToken:", accessToken);
                console.log("openId:", openId);

                // 构造请求URL
                const apiUrl = `https://graph.qq.com/user/get_user_info?access_token=${accessToken}&oauth_consumer_key=102805768&openid=${openId}`;
                console.log("API URL:", apiUrl);

                // 使用JSONP方式请求（避免跨域问题）
                const script = document.createElement('script');
                const callbackName = 'qqUserInfoCallback_' + Date.now();

                // 定义回调函数
                window[callbackName] = function(response) {
                    console.log("QQ API返回数据:", response);

                    if (response && response.ret === 0) {
                        updateStatus("登录成功，正在跳转...");
                        console.log("QQ API返回完整数据:");
                        console.log(JSON.stringify(response, null, 2));
                        console.log("昵称:", response.nickname);
                        console.log("所有头像字段:");
                        console.log("- figureurl:", response.figureurl);
                        console.log("- figureurl_1:", response.figureurl_1);
                        console.log("- figureurl_2:", response.figureurl_2);
                        console.log("- figureurl_qq_1:", response.figureurl_qq_1);
                        console.log("- figureurl_qq_2:", response.figureurl_qq_2);
                        console.log("- figureurl_qq:", response.figureurl_qq);

                        // 存储完整的用户信息到本地存储
                        const userInfo = {
                            nickname: response.nickname,
                            gender: response.gender,
                            province: response.province,
                            city: response.city,
                            figureurl: response.figureurl,
                            figureurl_1: response.figureurl_1,
                            figureurl_2: response.figureurl_2,
                            figureurl_qq_1: response.figureurl_qq_1,
                            figureurl_qq_2: response.figureurl_qq_2,
                            figureurl_qq: response.figureurl_qq,
                            accessToken: accessToken,
                            openId: openId,
                            loginTime: new Date().toISOString(),
                            loginMethod: 'qq_api'
                        };

                        localStorage.setItem('qqUserInfo', JSON.stringify(userInfo));
                        console.log("已保存完整用户信息:", userInfo);

                        // 通知父窗口登录成功并关闭当前窗口
                        updateStatus("登录完成，正在关闭窗口...");

                        // 尝试通知父窗口刷新
                        try {
                            if (window.opener && !window.opener.closed) {
                                // 通知父窗口登录成功
                                window.opener.postMessage({
                                    type: 'QQ_LOGIN_SUCCESS',
                                    userInfo: userInfo
                                }, '*');
                                console.log("已通知父窗口登录成功");

                                // 延迟关闭窗口，确保消息发送成功
                                setTimeout(() => {
                                    window.close();
                                }, 500);
                            } else {
                                // 如果没有父窗口，则跳转回原页面
                                const returnUrl = localStorage.getItem('returnUrl') || 'index.html';
                                console.log("没有父窗口，跳转到:", returnUrl);
                                window.location.href = returnUrl;
                            }
                        } catch (e) {
                            console.error("通知父窗口失败:", e);
                            // 备用方案：跳转回原页面
                            const returnUrl = localStorage.getItem('returnUrl') || 'index.html';
                            window.location.href = returnUrl;
                        }
                    } else {
                        console.error("QQ API返回错误:", response);
                        console.log("错误代码:", response?.ret);
                        console.log("错误信息:", response?.msg);

                        // API调用失败，使用基本信息
                        handleGetUserInfoError(accessToken, openId);
                    }

                    // 清理
                    try {
                        document.head.removeChild(script);
                        delete window[callbackName];
                    } catch (e) {
                        console.log("清理脚本时出错:", e);
                    }
                };

                // 设置错误处理
                script.onerror = function() {
                    console.error("JSONP请求失败");
                    handleGetUserInfoError(accessToken, openId);
                    try {
                        document.head.removeChild(script);
                        delete window[callbackName];
                    } catch (e) {
                        console.log("清理脚本时出错:", e);
                    }
                };

                // 添加JSONP回调参数并执行请求
                script.src = apiUrl + `&callback=${callbackName}`;
                document.head.appendChild(script);

                // 设置超时处理
                setTimeout(() => {
                    if (window[callbackName]) {
                        console.error("QQ API请求超时");
                        handleGetUserInfoError(accessToken, openId);
                        try {
                            document.head.removeChild(script);
                            delete window[callbackName];
                        } catch (e) {
                            console.log("清理脚本时出错:", e);
                        }
                    }
                }, 10000); // 10秒超时
            }

            // 使用基本登录信息完成登录
            function handleGetUserInfoError(accessToken, openId) {
                console.log("使用基本登录信息完成登录流程");

                // 保存基本的登录信息
                const userInfo = {
                    nickname: 'QQ用户' + (openId ? openId.substring(0, 6) : 'Unknown'),
                    figureurl_qq_1: 'https://q.qlogo.cn/headimg_dl/' + openId + '/100',
                    figureurl_qq_2: 'https://q.qlogo.cn/headimg_dl/' + openId + '/100',
                    accessToken: accessToken,
                    openId: openId,
                    loginTime: new Date().toISOString(),
                    loginMethod: 'qq_basic'
                };

                localStorage.setItem('qqUserInfo', JSON.stringify(userInfo));
                console.log("已保存用户信息:", userInfo);

                // 通知父窗口登录成功并关闭当前窗口
                updateStatus("登录完成，正在关闭窗口...");

                try {
                    if (window.opener && !window.opener.closed) {
                        // 通知父窗口登录成功
                        window.opener.postMessage({
                            type: 'QQ_LOGIN_SUCCESS',
                            userInfo: userInfo
                        }, '*');
                        console.log("已通知父窗口登录成功");

                        // 延迟关闭窗口
                        setTimeout(() => {
                            window.close();
                        }, 500);
                    } else {
                        // 备用方案：跳转回原页面
                        const returnUrl = localStorage.getItem('returnUrl') || 'index.html';
                        console.log("没有父窗口，跳转到:", returnUrl);
                        window.location.href = returnUrl;
                    }
                } catch (e) {
                    console.error("通知父窗口失败:", e);
                    // 备用方案：跳转回原页面
                    const returnUrl = localStorage.getItem('returnUrl') || 'index.html';
                    window.location.href = returnUrl;
                }
            }

            // 尝试多种方式解析参数
            let accessToken = null;
            let openId = null;

            // 方法1: 从hash中解析 (#access_token=xxx&openid=xxx)
            if (window.location.hash) {
                const hash = window.location.hash.substring(1);
                console.log("尝试从hash解析参数:", hash);
                const hashParams = new URLSearchParams(hash);
                accessToken = hashParams.get('access_token');
                openId = hashParams.get('openid');
                console.log("Hash解析结果 - accessToken:", accessToken, "openId:", openId);
            }

            // 方法2: 从query参数中解析 (?access_token=xxx&openid=xxx)
            if (!accessToken || !openId) {
                const search = window.location.search;
                console.log("尝试从search解析参数:", search);
                const searchParams = new URLSearchParams(search);
                accessToken = accessToken || searchParams.get('access_token');
                openId = openId || searchParams.get('openid');
                console.log("Search解析结果 - accessToken:", accessToken, "openId:", openId);
            }

            // 方法3: 尝试从QQ SDK获取
            if (!accessToken || !openId) {
                console.log("尝试从QQ SDK获取登录状态");
                try {
                    QC.Login.getMe(function(openid, access_token) {
                        console.log("QQ SDK返回 - openid:", openid, "access_token:", access_token);
                        if (openid && access_token) {
                            getUserInfo(access_token, openid);
                        } else {
                            console.log("QQ SDK未返回有效参数");
                            handleLoginFailure();
                        }
                    });
                    return; // 等待异步回调
                } catch (e) {
                    console.error("QQ SDK获取失败:", e);
                }
            }

            // 处理登录失败
            function handleLoginFailure() {
                console.log("登录失败，准备跳转回首页");
                alert("登录参数缺失，请重试");
                window.location.href = 'index.html';
            }

            if (accessToken && openId) {
                console.log("找到有效参数，开始获取详细用户信息");
                updateStatus("已获取登录凭证，正在获取用户信息...");
                // 调用QQ API获取详细用户信息
                getUserInfoByHttp(accessToken, openId);
            } else {
                console.log("未找到有效参数");
                updateStatus("正在等待登录凭证...");
                // 给QQ SDK一些时间来处理
                setTimeout(() => {
                    if (!accessToken || !openId) {
                        handleLoginFailure();
                    }
                }, 2000);
            }
        });
    </script>
</head>
<body>
    <div style="text-align: center; margin-top: 100px; font-family: Arial, sans-serif;">
        <h2>QQ登录处理中...</h2>
        <p id="statusText" style="color: #666; margin-top: 20px;">正在验证登录信息</p>
        <div style="margin-top: 20px;">
            <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    </div>
</body>
</html>