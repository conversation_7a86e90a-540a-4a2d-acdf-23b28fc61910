<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>登录处理中...</title>
    <script type="text/javascript" src="https://connect.qq.com/qc_jssdk.js" charset="utf-8"></script>
    <script>
        // 重写QC.Login.check方法，避免本地端口检查
        QC.Login.check = function(params) {
            if (typeof params.cb === "function") {
                // 直接从URL中获取openid和access_token
                const hash = window.location.hash.substring(1);
                const params = new URLSearchParams(hash);
                const openId = params.get('openid');
                const accessToken = params.get('access_token');
                
                if (openId && accessToken) {
                    // 模拟成功回调
                    setTimeout(() => params.cb(true, openId, accessToken), 500);
                } else {
                    // 模拟失败回调
                    setTimeout(() => params.cb(false), 500);
                }
            }
        };

        // 页面加载后处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log("页面加载完成，开始处理登录回调");
            console.log("当前URL:", window.location.href);
            console.log("Hash:", window.location.hash);
            console.log("Search:", window.location.search);

            // 获取用户信息
            function getUserInfo(accessToken, openId) {
                console.log("开始获取用户信息，accessToken:", accessToken, "openId:", openId);
                QC.api("get_user_info", {
                    accessToken: accessToken,
                    openid: openId
                })
                .success(function(response) {
                    console.log("获取用户信息成功:", response);
                    // 存储用户信息到本地存储
                    localStorage.setItem('qqUserInfo', JSON.stringify({
                        ...response.data,
                        accessToken: accessToken,
                        openId: openId
                    }));

                    // 跳转回原页面或首页
                    const returnUrl = localStorage.getItem('returnUrl') || 'index.html';
                    console.log("准备跳转到:", returnUrl);
                    window.location.href = returnUrl;
                })
                .error(function(error) {
                    console.error("获取用户信息失败:", error);
                    alert("获取用户信息失败，请重试");
                    window.location.href = 'index.html';
                });
            }

            // 尝试多种方式解析参数
            let accessToken = null;
            let openId = null;

            // 方法1: 从hash中解析 (#access_token=xxx&openid=xxx)
            if (window.location.hash) {
                const hash = window.location.hash.substring(1);
                console.log("尝试从hash解析参数:", hash);
                const hashParams = new URLSearchParams(hash);
                accessToken = hashParams.get('access_token');
                openId = hashParams.get('openid');
                console.log("Hash解析结果 - accessToken:", accessToken, "openId:", openId);
            }

            // 方法2: 从query参数中解析 (?access_token=xxx&openid=xxx)
            if (!accessToken || !openId) {
                const search = window.location.search;
                console.log("尝试从search解析参数:", search);
                const searchParams = new URLSearchParams(search);
                accessToken = accessToken || searchParams.get('access_token');
                openId = openId || searchParams.get('openid');
                console.log("Search解析结果 - accessToken:", accessToken, "openId:", openId);
            }

            // 方法3: 尝试从QQ SDK获取
            if (!accessToken || !openId) {
                console.log("尝试从QQ SDK获取登录状态");
                try {
                    QC.Login.getMe(function(openid, access_token) {
                        console.log("QQ SDK返回 - openid:", openid, "access_token:", access_token);
                        if (openid && access_token) {
                            getUserInfo(access_token, openid);
                        } else {
                            console.log("QQ SDK未返回有效参数");
                            handleLoginFailure();
                        }
                    });
                    return; // 等待异步回调
                } catch (e) {
                    console.error("QQ SDK获取失败:", e);
                }
            }

            // 处理登录失败
            function handleLoginFailure() {
                console.log("登录失败，准备跳转回首页");
                alert("登录参数缺失，请重试");
                window.location.href = 'index.html';
            }

            if (accessToken && openId) {
                console.log("找到有效参数，开始获取用户信息");
                getUserInfo(accessToken, openId);
            } else {
                console.log("未找到有效参数");
                // 给QQ SDK一些时间来处理
                setTimeout(() => {
                    if (!accessToken || !openId) {
                        handleLoginFailure();
                    }
                }, 2000);
            }
        });
    </script>
</head>
<body>
    <div style="text-align: center; margin-top: 100px;">
        <h2>登录处理中，请稍候...</h2>
    </div>
</body>
</html>