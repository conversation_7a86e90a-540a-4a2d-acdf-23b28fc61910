<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="装逼大全交流群 - 科技爱好者的聚集地，分享最新科技动态，探讨未来发展趋势，在这里装逼是一门艺术">
    <meta name="keywords" content="装逼大全,QQ群,科技交流,知识分享,技术讨论,群聊">
    <meta name="author" content="装逼大全交流群">
    <meta property="og:title" content="装逼大全交流群 - 科技与装逼的完美结合">
    <meta property="og:description" content="科技爱好者的聚集地，100+群成员，999+每日消息，5.0群评分">
    <meta property="og:type" content="website">
    <meta property="og:image" content="imgs/logomob.png">
    <title>装逼大全交流群 - 科技与装逼的完美结合</title>
    <link rel="icon" type="image/png" href="imgs/logomob.png">
    <link rel="stylesheet" href="qq-group.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <!-- 添加QQ登录SDK -->
    <script type="text/javascript" src="https://connect.qq.com/qc_jssdk.js" data-appid="102805768" data-redirecturi="https://zhuangbidaquan.xyz/auth-callback.html" charset="utf-8"></script>
</head>
<body>
    <div class="stars"></div>
    <div class="twinkling"></div>

    <nav class="navbar">
        <div class="nav-brand">
            <i class="fas fa-rocket"></i>
            装逼大全交流群
        </div>
        <button class="menu-toggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="nav-links">
            <a href="#home" class="active">首页</a>
            <a href="hall.html">名人堂</a>
            <a href="vote.html">年度投票</a>
            <a href="chat-records/index.html">聊天记录分析</a>
            <div class="dropdown">
                <a href="#" class="dropdown-toggle">
                    小游戏 <i class="fas fa-chevron-down"></i>
                </a>
                <div class="dropdown-menu">
                    <a href="avoid-germs.html">躲避细菌</a>
                    <a href="stock-simulator.html">模拟交♂易</a>
                    <a href="http://zhuangbidaquan.xyz:8005/">圣龙大乱斗</a>
                    <a href="lonelyHorseman/index.html">一骑当千</a>
                    <a href="Link/index.html">连连看</a>
                    <a href="chatWithBu/index.html">与BU对谈</a>
                    <a href="xiangqi-survival/index.html">象棋生存</a>
                    <a href="purchasing-power/">购买力测试</a>
                    <a href="xiaomifeng/index.html/">小蜜蜂</a>
                </div>
            </div>
            <a href="http://zhuangbidaquan.xyz:8009/">装逼论坛</a>
            <a href="new.html">新版主页</a>
            <!-- 在导航栏添加QQ登录按钮 -->
            <div id="qqLoginBtn" class="qq-login-btn"></div>
            
        <div class="user-info" id="userInfo" style="display: none;">
        <img id="userAvatar" class="user-avatar" src="" alt="用户头像">
        <span id="userNickname" class="user-nickname"></span>
        <button id="logoutBtn" class="logout-btn">退出</button>
    </div>
    <div id="qqLoginBtn" class="qq-login-btn"></div>
        </div>
    </nav>

    <!-- 其他原有内容保持不变 -->
         <section id="home" class="hero-section">
        <div class="hero-content">
            <h1 class="glitch" data-text="装逼大全交流群">装逼大全交流群</h1>
            <p class="subtitle">在这里，装逼是一门艺术</p>
            <div class="hero-stats">
                <div class="stat-item" data-count="100">
                    <i class="fas fa-users"></i>
                    <span class="stat-number">0+</span>
                    <span class="stat-label">群成员</span>
                </div>
                <div class="stat-item" data-count="999">
                    <i class="fas fa-comments"></i>
                    <span class="stat-number">0+</span>
                    <span class="stat-label">每日消息</span>
                </div>
                <div class="stat-item" data-count="5">
                    <i class="fas fa-star"></i>
                    <span class="stat-number">0.0</span>
                    <span class="stat-label">群评分</span>
                </div>
            </div>
            <div class="hero-actions">
                <a href="#join" class="cta-button primary">
                    <i class="fab fa-qq"></i>
                    立即加入群聊
                </a>
                <a href="vote.html" class="cta-button secondary">
                    <i class="fas fa-trophy"></i>
                    参与年度投票
                </a>
            </div>
        </div>
        <div class="hero-decoration">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
    </section>

    <section id="newMenu">
        <ul>
            <li style="--i:6"><a href="#home">首页</a></li>
            <li style="--i:5"><a href="http://zhuangbidaquan.xyz:8005">圣龙大乱斗</a></li>
            <li style="--i:4"><a href="http://zhuangbidaquan.xyz:8009">装逼论坛</a></li>
            <li style="--i:3"><a href="hall.html">名人堂</a></li>
            <li style="--i:2"><a href="vote.html">年度投票</a></li>
            <li style="--i:1"><a href="new.html">新版主页</a></li>
        </ul>
    </section>


    <section id="features" class="features-section">
        <h2>群特色</h2>
        <div class="features-grid">
            <div class="feature-card">
                <i class="fas fa-rocket"></i>
                <h3>科技前沿</h3>
                <p>分享最新科技动态，探讨未来发展趋势</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-brain"></i>
                <h3>知识交流</h3>
                <p>各领域大神在线解答，知识碰撞产生火花</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-laugh-squint"></i>
                <h3>欢乐氛围</h3>
                <p>轻松愉快的聊天环境，让装逼更有趣</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-trophy"></i>
                <h3>装逼大赛</h3>
                <p>定期举办装逼大赛，展示你的独特魅力</p>
            </div>
        </div>
    </section>

    <section id="members" class="members-section">
        <h2>群成员风采</h2>
        <div class="members-grid">
            <div class="member-card">
                <div class="member-avatar">
                    <i class="fas fa-crown"></i>
                </div>
                <h3>群主大大</h3>
                <p>装逼界天花板</p>
            </div>
            <div class="member-card">
                <div class="member-avatar">
                    <i class="fas fa-star"></i>
                </div>
                <h3>技术大佬</h3>
                <p>代码界的装逼王</p>
            </div>
            <div class="member-card">
                <div class="member-avatar">
                    <i class="fas fa-gem"></i>
                </div>
                <h3>生活达人</h3>
                <p>品质生活的代言人</p>
            </div>
        </div>
    </section>

    <section id="rules" class="rules-section">
        <h2>群规</h2>
        <div class="rules-container">
            <div class="rule-card">
                <i class="fas fa-check-circle"></i>
                <p>文明交流，禁止人身攻击</p>
            </div>
            <div class="rule-card">
                <i class="fas fa-check-circle"></i>
                <p>适度装逼，拒绝过度炫耀</p>
            </div>
            <div class="rule-card">
                <i class="fas fa-check-circle"></i>
                <p>分享知识，拒绝灌水</p>
            </div>
            <div class="rule-card">
                <i class="fas fa-check-circle"></i>
                <p>互相尊重，共建和谐群聊</p>
            </div>
        </div>
    </section>
<!-- 其他原有内容保持不变 -->

    <section id="join" class="join-section">
        <h2>加入我们</h2>
        <div class="join-container">
            <div class="qr-code">
                <img src="imgs/qrcode.jpg" alt="QQ群二维码">
            </div>
            <div class="join-info">
                <h3>群号：616978142</h3>
                <p>期待你的加入，让我们一起装逼！</p>
                <!-- 在加入区域添加更大的QQ登录按钮 -->
                <div id="qqLoginBtnLarge" class="qq-login-btn large"></div>
                <button class="join-btn">
                    <i class="fab fa-qq"></i>
                    立即加入
                </button>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-brand">
                <i class="fab fa-qq"></i>
                <span>装逼大全交流群</span>
            </div>
            <div class="footer-links">
                <a href="#home">首页</a>
                <a href="#features">群特色</a>
                <a href="#members">群成员</a>
                <a href="#rules">群规</a>
                <a href="#join">加入我们</a>
                <a href="hall.html">名人堂</a>
                <a href="vote.html">年度投票</a>
            </div>
            <!-- 在页脚添加QQ登录链接 -->
            <div class="footer-login">
                <span id="qqLoginBtnFooter"></span>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; <span id="currentYear">2024</span> 装逼大全交流群. All rights reserved.</p>
        </div>
    </footer>

    <script src="qq-group.js"></script>
    <!-- QQ登录按钮现在由qq-group.js统一管理 -->
</body>
</html>