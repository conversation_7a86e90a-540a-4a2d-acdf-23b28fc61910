document.addEventListener('DOMContentLoaded', () => {
    // 导航栏滚动效果
    const navbar = document.querySelector('.navbar');
    let lastScroll = 0;

    // 存储当前页面URL，用于登录后跳转回来
    localStorage.setItem('returnUrl', window.location.pathname);

    // 初始化QQ登录按钮
    function initQQLogin() {
        console.log("初始化QQ登录按钮");
        const redirectUri = window.location.origin + "/auth-callback.html";
        console.log("回调URL:", redirectUri);

        QC.Login({
            btnId: "qqLoginBtn",
            size: "medium",
            scope: "get_user_info",
            redirectURI: encodeURIComponent(redirectUri)
        });
    }

    // QQ登录成功回调
    function onLoginSuccess() {
        // 获取用户信息
        QC.api("get_user_info", {})
            .success(function(response) {
                // 显示用户信息
                document.getElementById('userAvatar').src = response.data.figureurl_qq_1 || response.data.figureurl_qq_2;
                document.getElementById('userNickname').textContent = response.data.nickname;
                
                // 显示用户信息区域，隐藏登录按钮
                document.getElementById('userInfo').style.display = 'flex';
                document.getElementById('qqLoginBtn').style.display = 'none';
                
                // 存储用户信息到本地
                localStorage.setItem('qqUserInfo', JSON.stringify(response.data));
            })
            .error(function(error) {
                console.error("获取用户信息失败:", error);
                alert("获取用户信息失败，请重试");
            });
    }

    // 退出登录
    function logout() {
        QC.Login.signOut();
        document.getElementById('userInfo').style.display = 'none';
        document.getElementById('qqLoginBtn').style.display = 'block';
        localStorage.removeItem('qqUserInfo');
    }

    // 检查登录状态
    function checkLoginStatus() {
        const userInfo = localStorage.getItem('qqUserInfo');
        if(userInfo) {
            try {
                const data = JSON.parse(userInfo);
                console.log("检查到本地用户信息:", data);

                // 检查是否有有效的登录信息
                if((data.accessToken && data.openId) || (data.accessToken && data.openid)) {
                    // 有本地存储的用户信息，视为已登录
                    displayUserInfo(data);
                    return;
                }
            } catch(e) {
                console.error("解析用户信息失败:", e);
            }
        }
        console.log("未找到有效的登录信息");
    }
    function displayUserInfo(data) {
        console.log("显示用户信息:", data);

        // 设置用户头像
        const avatar = data.figureurl_qq_1 || data.figureurl_qq_2 || 'https://q.qlogo.cn/headimg_dl/' + (data.openId || data.openid) + '/100';
        document.getElementById('userAvatar').src = avatar;

        // 设置用户昵称
        const nickname = data.nickname || 'QQ用户';
        document.getElementById('userNickname').textContent = nickname;

        // 显示用户信息区域，隐藏登录按钮
        document.getElementById('userInfo').style.display = 'flex';
        document.getElementById('qqLoginBtn').style.display = 'none';

        console.log("用户信息显示完成");
    }
    // 初始化登录功能
    function initLogin() {
        // 初始化QQ登录按钮
        initQQLogin();
        
        // 绑定退出按钮事件
        const logoutBtn = document.getElementById('logoutBtn');
        if(logoutBtn) {
            logoutBtn.addEventListener('click', logout);
        }
        
        // 检查登录状态
        checkLoginStatus();
        
        // 监听QQ登录成功事件
        QC.Login.getMe(function(openId, accessToken) {
            console.log("QQ登录成功回调 - openId:", openId, "accessToken:", accessToken);
            if (openId && accessToken) {
                onLoginSuccess();
            }
        });
    }

    // 初始化登录功能
    initLogin();

    // 以下是你原有的其他功能代码，保持不变
    window.addEventListener('scroll', () => {
        const currentScroll = window.pageYOffset;
        
        if (currentScroll <= 0) {
            navbar.classList.remove('scroll-up');
            return;
        }
        
        if (currentScroll > lastScroll && !navbar.classList.contains('scroll-down')) {
            navbar.classList.remove('scroll-up');
            navbar.classList.add('scroll-down');
        } else if (currentScroll < lastScroll && navbar.classList.contains('scroll-down')) {
            navbar.classList.remove('scroll-down');
            navbar.classList.add('scroll-up');
        }
        lastScroll = currentScroll;
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 数字增长动画
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach(item => {
        const statNumber = item.querySelector('.stat-number');
        const targetCount = parseInt(item.dataset.count);
        let current = 0;
        const increment = targetCount / 50;
        const duration = 2000;
        const interval = duration / 50;

        const updateNumber = () => {
            current += increment;
            if (current < targetCount) {
                if (item.dataset.count === '5') {
                    statNumber.textContent = (current).toFixed(1);
                } else {
                    statNumber.textContent = Math.floor(current) + '+';
                }
                setTimeout(updateNumber, interval);
            } else {
                if (item.dataset.count === '5') {
                    statNumber.textContent = '5.0';
                } else {
                    statNumber.textContent = targetCount + '+';
                }
            }
        };

        // 当元素进入视口时开始动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateNumber();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(item);
    });

    // 特性卡片动画
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px)';
        });
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
        });
    });

    // CTA按钮点击效果
    const ctaButtons = document.querySelectorAll('.cta-button');
    ctaButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            if (btn.href && btn.href.includes('#join')) {
                e.preventDefault();
                // 滚动到加入部分
                document.querySelector('#join').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 加入按钮点击效果
    const joinBtn = document.querySelector('.join-btn');
    if (joinBtn) {
        joinBtn.addEventListener('click', () => {
            // 这里可以添加实际的加入群聊逻辑
            alert('欢迎加入装逼大全交流群！群号：616978142');
        });
    }

    // 自动更新年份
    const currentYearElement = document.getElementById('currentYear');
    if (currentYearElement) {
        currentYearElement.textContent = new Date().getFullYear();
    }

    // 添加打字机效果
    const subtitle = document.querySelector('.subtitle');
    if (subtitle) {
        const text = subtitle.textContent;
        subtitle.textContent = '';
        let i = 0;
        
        const typeWriter = () => {
            if (i < text.length) {
                subtitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };
        
        // 当元素进入视口时开始打字效果
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    typeWriter();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(subtitle);
    }

    // 移动端菜单交互
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    const navLinksItems = document.querySelectorAll('.nav-links a');

    if (menuToggle && navLinks) {
        // 切换菜单显示/隐藏
        menuToggle.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            navLinks.classList.toggle('active');
            // 切换菜单图标
            const icon = menuToggle.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-bars');
                icon.classList.toggle('fa-times');
            }
        });

        // 点击导航链接时关闭菜单
        navLinksItems.forEach(link => {
            link.addEventListener('click', () => {
                navLinks.classList.remove('active');
                const icon = menuToggle.querySelector('i');
                if (icon) {
                    icon.classList.add('fa-bars');
                    icon.classList.remove('fa-times');
                }
            });
        });

        // 点击页面其他区域关闭菜单
        document.addEventListener('click', (e) => {
            if (navLinks.classList.contains('active') && 
                !navLinks.contains(e.target) && 
                !menuToggle.contains(e.target)) {
                navLinks.classList.remove('active');
                const icon = menuToggle.querySelector('i');
                if (icon) {
                    icon.classList.add('fa-bars');
                    icon.classList.remove('fa-times');
                }
            }
        });
    }
});