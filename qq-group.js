document.addEventListener('DOMContentLoaded', () => {
    // 导航栏滚动效果
    const navbar = document.querySelector('.navbar');
    let lastScroll = 0;

    // 存储当前页面URL，用于登录后跳转回来
    localStorage.setItem('returnUrl', window.location.pathname);

    // 创建QQ登录按钮HTML
    function createQQLoginButton(size = 'medium') {
        const sizes = {
            small: { padding: '6px 12px', fontSize: '12px' },
            medium: { padding: '8px 16px', fontSize: '14px' },
            large: { padding: '12px 24px', fontSize: '16px' }
        };

        const sizeStyle = sizes[size] || sizes.medium;

        return `
            <div style="
                background: linear-gradient(45deg, #12b7f5, #1e90ff);
                color: white;
                padding: ${sizeStyle.padding};
                border-radius: 20px;
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                font-size: ${sizeStyle.fontSize};
                border: none;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(18, 183, 245, 0.3);
            " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <i class="fab fa-qq"></i>
                QQ登录
            </div>
        `;
    }

    // 初始化QQ登录按钮
    function initQQLogin() {
        console.log("初始化QQ登录按钮");

        // 初始化所有QQ登录按钮
        const loginButtons = [
            { id: "qqLoginBtn", size: "medium" },
            { id: "qqLoginBtnLarge", size: "large" },
            { id: "qqLoginBtnFooter", size: "small" }
        ];

        loginButtons.forEach(({ id, size }) => {
            const loginBtn = document.getElementById(id);
            if (loginBtn) {
                loginBtn.innerHTML = createQQLoginButton(size);
                loginBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    openQQLoginWindow();
                });
                console.log(`已初始化登录按钮: ${id}`);
            }
        });
    }

    // 在新窗口中打开QQ登录
    function openQQLoginWindow() {
        console.log("打开QQ登录窗口");

        // 构建QQ登录URL
        const appId = '102805768';
        const redirectUri = encodeURIComponent(window.location.origin + "/auth-callback.html");
        const scope = 'get_user_info';
        const state = Math.random().toString(36).substring(2);

        const loginUrl = `https://graph.qq.com/oauth2.0/authorize?response_type=token&client_id=${appId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}`;

        console.log("登录URL:", loginUrl);

        // 在新窗口中打开登录页面
        const loginWindow = window.open(
            loginUrl,
            'qqLogin',
            'width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes,toolbar=no,menubar=no'
        );

        if (loginWindow) {
            // 监听窗口关闭
            const checkClosed = setInterval(() => {
                if (loginWindow.closed) {
                    clearInterval(checkClosed);
                    console.log("登录窗口已关闭");
                    // 检查登录状态
                    setTimeout(() => {
                        checkLoginStatus();
                    }, 500);
                }
            }, 1000);
        } else {
            alert('无法打开登录窗口，请检查浏览器弹窗设置');
        }
    }



    // 退出登录
    function logout() {
        QC.Login.signOut();
        document.getElementById('userInfo').style.display = 'none';
        document.getElementById('qqLoginBtn').style.display = 'block';
        localStorage.removeItem('qqUserInfo');
    }

    // 检查登录状态
    function checkLoginStatus() {
        const userInfo = localStorage.getItem('qqUserInfo');
        if(userInfo) {
            try {
                const data = JSON.parse(userInfo);
                console.log("检查到本地用户信息:", data);

                // 检查是否有有效的登录信息
                if((data.accessToken && data.openId) || (data.accessToken && data.openid)) {
                    // 有本地存储的用户信息，视为已登录
                    displayUserInfo(data);
                    return;
                }
            } catch(e) {
                console.error("解析用户信息失败:", e);
            }
        }
        console.log("未找到有效的登录信息");
    }
    function displayUserInfo(data) {
        console.log("显示用户信息:", data);

        // 设置用户头像 - 优先使用QQ API返回的头像
        let avatar = '';
        if (data.figureurl_qq_2) {
            avatar = data.figureurl_qq_2; // 100x100 QQ头像
        } else if (data.figureurl_qq_1) {
            avatar = data.figureurl_qq_1; // 40x40 QQ头像
        } else if (data.figureurl_2) {
            avatar = data.figureurl_2; // 100x100 头像
        } else if (data.figureurl_1) {
            avatar = data.figureurl_1; // 40x40 头像
        } else {
            // 备用头像
            avatar = 'https://q.qlogo.cn/headimg_dl/' + (data.openId || data.openid) + '/100';
        }

        document.getElementById('userAvatar').src = avatar;
        console.log("设置用户头像:", avatar);

        // 设置用户昵称
        const nickname = data.nickname || 'QQ用户';
        document.getElementById('userNickname').textContent = nickname;
        console.log("设置用户昵称:", nickname);

        // 显示用户信息区域，隐藏登录按钮
        document.getElementById('userInfo').style.display = 'flex';
        document.getElementById('qqLoginBtn').style.display = 'none';

        console.log("用户信息显示完成");
    }
    // 监听来自登录窗口的消息
    function setupMessageListener() {
        window.addEventListener('message', function(event) {
            console.log("收到消息:", event.data);

            if (event.data && event.data.type === 'QQ_LOGIN_SUCCESS') {
                console.log("收到QQ登录成功消息:", event.data.userInfo);

                // 显示用户信息
                displayUserInfo(event.data.userInfo);

                // 可选：显示登录成功提示
                showLoginSuccessMessage(event.data.userInfo.nickname);
            }
        });
    }

    // 显示登录成功消息
    function showLoginSuccessMessage(nickname) {
        // 创建临时提示消息
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            animation: slideIn 0.3s ease-out;
        `;
        message.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-check-circle"></i>
                <span>欢迎回来，${nickname}！</span>
            </div>
        `;

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(message);

        // 3秒后自动移除
        setTimeout(() => {
            message.style.animation = 'slideIn 0.3s ease-out reverse';
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            }, 300);
        }, 3000);
    }

    // 初始化登录功能
    function initLogin() {
        // 初始化QQ登录按钮
        initQQLogin();

        // 设置消息监听器
        setupMessageListener();

        // 绑定退出按钮事件
        const logoutBtn = document.getElementById('logoutBtn');
        if(logoutBtn) {
            logoutBtn.addEventListener('click', logout);
        }

        // 检查登录状态
        checkLoginStatus();
    }

    // 初始化登录功能
    initLogin();

    // 以下是你原有的其他功能代码，保持不变
    window.addEventListener('scroll', () => {
        const currentScroll = window.pageYOffset;
        
        if (currentScroll <= 0) {
            navbar.classList.remove('scroll-up');
            return;
        }
        
        if (currentScroll > lastScroll && !navbar.classList.contains('scroll-down')) {
            navbar.classList.remove('scroll-up');
            navbar.classList.add('scroll-down');
        } else if (currentScroll < lastScroll && navbar.classList.contains('scroll-down')) {
            navbar.classList.remove('scroll-down');
            navbar.classList.add('scroll-up');
        }
        lastScroll = currentScroll;
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 数字增长动画
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach(item => {
        const statNumber = item.querySelector('.stat-number');
        const targetCount = parseInt(item.dataset.count);
        let current = 0;
        const increment = targetCount / 50;
        const duration = 2000;
        const interval = duration / 50;

        const updateNumber = () => {
            current += increment;
            if (current < targetCount) {
                if (item.dataset.count === '5') {
                    statNumber.textContent = (current).toFixed(1);
                } else {
                    statNumber.textContent = Math.floor(current) + '+';
                }
                setTimeout(updateNumber, interval);
            } else {
                if (item.dataset.count === '5') {
                    statNumber.textContent = '5.0';
                } else {
                    statNumber.textContent = targetCount + '+';
                }
            }
        };

        // 当元素进入视口时开始动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateNumber();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(item);
    });

    // 特性卡片动画
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px)';
        });
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
        });
    });

    // CTA按钮点击效果
    const ctaButtons = document.querySelectorAll('.cta-button');
    ctaButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            if (btn.href && btn.href.includes('#join')) {
                e.preventDefault();
                // 滚动到加入部分
                document.querySelector('#join').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 加入按钮点击效果
    const joinBtn = document.querySelector('.join-btn');
    if (joinBtn) {
        joinBtn.addEventListener('click', () => {
            // 这里可以添加实际的加入群聊逻辑
            alert('欢迎加入装逼大全交流群！群号：616978142');
        });
    }

    // 自动更新年份
    const currentYearElement = document.getElementById('currentYear');
    if (currentYearElement) {
        currentYearElement.textContent = new Date().getFullYear();
    }

    // 添加打字机效果
    const subtitle = document.querySelector('.subtitle');
    if (subtitle) {
        const text = subtitle.textContent;
        subtitle.textContent = '';
        let i = 0;
        
        const typeWriter = () => {
            if (i < text.length) {
                subtitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };
        
        // 当元素进入视口时开始打字效果
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    typeWriter();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(subtitle);
    }

    // 移动端菜单交互
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    const navLinksItems = document.querySelectorAll('.nav-links a');

    if (menuToggle && navLinks) {
        // 切换菜单显示/隐藏
        menuToggle.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            navLinks.classList.toggle('active');
            // 切换菜单图标
            const icon = menuToggle.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-bars');
                icon.classList.toggle('fa-times');
            }
        });

        // 点击导航链接时关闭菜单
        navLinksItems.forEach(link => {
            link.addEventListener('click', () => {
                navLinks.classList.remove('active');
                const icon = menuToggle.querySelector('i');
                if (icon) {
                    icon.classList.add('fa-bars');
                    icon.classList.remove('fa-times');
                }
            });
        });

        // 点击页面其他区域关闭菜单
        document.addEventListener('click', (e) => {
            if (navLinks.classList.contains('active') && 
                !navLinks.contains(e.target) && 
                !menuToggle.contains(e.target)) {
                navLinks.classList.remove('active');
                const icon = menuToggle.querySelector('i');
                if (icon) {
                    icon.classList.add('fa-bars');
                    icon.classList.remove('fa-times');
                }
            }
        });
    }
});