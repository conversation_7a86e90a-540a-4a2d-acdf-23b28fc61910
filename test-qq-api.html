<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #12b7f5;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0ea5e9;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .user-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ API测试页面</h1>
        
        <div class="test-section">
            <h3>测试参数</h3>
            <p><strong>Access Token:</strong> 4EF28A7E23C26036DA408183FDCF1F37</p>
            <p><strong>Open ID:</strong> EED9B676DEE5A7D655EA118E598857CD</p>
        </div>
        
        <div class="test-section">
            <h3>操作</h3>
            <button onclick="testQQAPI()">测试QQ API请求</button>
            <button onclick="checkStorage()">检查本地存储</button>
            <button onclick="clearStorage()">清除存储</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="test-section">
            <h3>用户信息</h3>
            <div id="userInfo">暂无数据</div>
        </div>
        
        <div class="test-section">
            <h3>日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        const accessToken = "4EF28A7E23C26036DA408183FDCF1F37";
        const openId = "EED9B676DEE5A7D655EA118E598857CD";

        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logArea').textContent = '';
        }

        function testQQAPI() {
            log("开始测试QQ API请求");
            log(`使用参数 - accessToken: ${accessToken}, openId: ${openId}`);

            const appId = "102805768";
            
            fetch(`https://graph.qq.com/user/get_user_info?access_token=${accessToken}&oauth_consumer_key=${appId}&openid=${openId}`)
                .then(response => {
                    log(`HTTP状态: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    log("收到API响应:");
                    log(JSON.stringify(data, null, 2));
                    
                    if (data.ret === 0) {
                        log("API请求成功");
                        console.log("昵称:", data.nickname);
                        console.log("头像:", data.figureurl_qq_2);
                        
                        // 存储用户信息
                        const userInfo = {
                            nickname: data.nickname,
                            gender: data.gender,
                            province: data.province,
                            city: data.city,
                            figureurl: data.figureurl,
                            figureurl_1: data.figureurl_1,
                            figureurl_2: data.figureurl_2,
                            figureurl_qq_1: data.figureurl_qq_1,
                            figureurl_qq_2: data.figureurl_qq_2,
                            figureurl_qq: data.figureurl_qq,
                            accessToken: accessToken,
                            openId: openId,
                            loginTime: new Date().toISOString(),
                            loginMethod: 'fetch_api'
                        };

                        localStorage.setItem('qqUserInfo', JSON.stringify(userInfo));
                        log("用户信息已保存到localStorage");
                        displayUserInfo(userInfo);
                        
                    } else {
                        log(`API请求失败 - 错误代码: ${data.ret}, 错误信息: ${data.msg}`);
                        console.error("获取用户信息失败:", data.msg);
                        useBasicUserInfo();
                    }
                })
                .catch(error => {
                    log(`Fetch请求失败: ${error.message}`);
                    console.error("请求异常:", error);
                    useBasicUserInfo();
                });
        }

        function useBasicUserInfo() {
            log("使用基本用户信息");
            
            const userInfo = {
                nickname: 'QQ用户' + openId.substring(0, 6),
                gender: '未知',
                province: '未知',
                city: '未知',
                figureurl_qq_1: `https://q.qlogo.cn/headimg_dl/${openId}/40`,
                figureurl_qq_2: `https://q.qlogo.cn/headimg_dl/${openId}/100`,
                accessToken: accessToken,
                openId: openId,
                loginTime: new Date().toISOString(),
                loginMethod: 'basic_fallback'
            };

            localStorage.setItem('qqUserInfo', JSON.stringify(userInfo));
            log("基本用户信息已保存到localStorage");
            displayUserInfo(userInfo);
        }

        function displayUserInfo(userInfo) {
            const userInfoDiv = document.getElementById('userInfo');
            const avatar = userInfo.figureurl_qq_2 || userInfo.figureurl_qq_1 || `https://q.qlogo.cn/headimg_dl/${userInfo.openId}/100`;
            
            userInfoDiv.innerHTML = `
                <div class="user-info">
                    <img src="${avatar}" alt="头像" style="width: 50px; height: 50px; border-radius: 50%; margin-right: 10px;">
                    <strong>昵称:</strong> ${userInfo.nickname}<br>
                    <strong>性别:</strong> ${userInfo.gender}<br>
                    <strong>地区:</strong> ${userInfo.province} ${userInfo.city}<br>
                    <strong>登录时间:</strong> ${userInfo.loginTime}
                </div>
            `;
        }

        function checkStorage() {
            const userInfo = localStorage.getItem('qqUserInfo');
            if (userInfo) {
                try {
                    const data = JSON.parse(userInfo);
                    log("本地存储中的用户信息:");
                    log(JSON.stringify(data, null, 2));
                    displayUserInfo(data);
                } catch (e) {
                    log("解析本地存储失败: " + e.message);
                }
            } else {
                log("本地存储中无用户信息");
                document.getElementById('userInfo').innerHTML = '暂无数据';
            }
        }

        function clearStorage() {
            localStorage.removeItem('qqUserInfo');
            log("本地存储已清除");
            document.getElementById('userInfo').innerHTML = '暂无数据';
        }

        // 页面加载时检查存储
        document.addEventListener('DOMContentLoaded', function() {
            log("测试页面加载完成");
            checkStorage();
        });
    </script>
</body>
</html>

