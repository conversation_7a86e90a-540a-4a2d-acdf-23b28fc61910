<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>登录处理中...</title>
    <script type="text/javascript" src="https://connect.qq.com/qc_jssdk.js" charset="utf-8"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>登录处理中...</h2>
        <div class="spinner"></div>
        <div class="status" id="status">正在验证登录信息</div>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }

        function completeLogin(accessToken, openId) {
            updateStatus('登录成功，正在跳转...');
            
            // 保存基本的登录信息
            const userInfo = {
                nickname: 'QQ用户' + openId.substring(0, 6),
                figureurl_qq_1: `https://q.qlogo.cn/headimg_dl/${openId}/100`,
                figureurl_qq_2: `https://q.qlogo.cn/headimg_dl/${openId}/100`,
                accessToken: accessToken,
                openId: openId,
                loginTime: new Date().toISOString(),
                loginMethod: 'qq_simple'
            };
            
            localStorage.setItem('qqUserInfo', JSON.stringify(userInfo));
            console.log("已保存用户信息:", userInfo);
            
            // 跳转回原页面或首页
            const returnUrl = localStorage.getItem('returnUrl') || 'index.html';
            console.log("准备跳转到:", returnUrl);
            
            setTimeout(() => {
                window.location.href = returnUrl;
            }, 500);
        }

        function handleLoginFailure() {
            updateStatus('登录失败，正在返回...');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }

        // 页面加载后处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log("简化版登录回调页面加载完成");
            console.log("当前URL:", window.location.href);
            
            updateStatus('正在解析登录参数...');
            
            let accessToken = null;
            let openId = null;
            
            // 从hash中解析参数
            if (window.location.hash) {
                const hash = window.location.hash.substring(1);
                const hashParams = new URLSearchParams(hash);
                accessToken = hashParams.get('access_token');
                console.log("从hash获取到accessToken:", accessToken);
            }
            
            // 从query参数中解析
            if (window.location.search) {
                const searchParams = new URLSearchParams(window.location.search);
                accessToken = accessToken || searchParams.get('access_token');
                openId = openId || searchParams.get('openid');
                console.log("从search获取到参数 - accessToken:", accessToken, "openId:", openId);
            }
            
            if (accessToken) {
                updateStatus('正在获取用户标识...');
                
                // 尝试从QQ SDK获取openId
                try {
                    QC.Login.getMe(function(qqOpenId, qqAccessToken) {
                        console.log("QQ SDK返回 - openId:", qqOpenId, "accessToken:", qqAccessToken);
                        if (qqOpenId) {
                            completeLogin(accessToken, qqOpenId);
                        } else {
                            handleLoginFailure();
                        }
                    });
                } catch (e) {
                    console.error("QQ SDK调用失败:", e);
                    handleLoginFailure();
                }
                
                // 设置超时处理
                setTimeout(() => {
                    if (!openId) {
                        console.log("获取openId超时");
                        handleLoginFailure();
                    }
                }, 5000);
            } else {
                console.log("未找到accessToken");
                handleLoginFailure();
            }
        });
    </script>
</body>
</html>
