<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ登录测试页面</title>
    <script type="text/javascript" src="https://connect.qq.com/qc_jssdk.js" data-appid="102805768" data-redirecturi="https://zhuangbidaquan.xyz/auth-callback.html" charset="utf-8"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .log-area {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #12b7f5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0ea5e9;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ登录测试页面</h1>
        
        <div class="test-section">
            <h3>当前页面信息</h3>
            <div id="pageInfo"></div>
        </div>
        
        <div class="test-section">
            <h3>QQ登录按钮</h3>
            <div id="qqLoginBtn"></div>
            <button onclick="initQQLogin()">重新初始化登录按钮</button>
        </div>
        
        <div class="test-section">
            <h3>登录状态检查</h3>
            <button onclick="checkLoginStatus()">检查登录状态</button>
            <button onclick="clearStorage()">清除本地存储</button>
            <div id="loginStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>调试日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logArea').textContent = '';
        }

        // 显示页面信息
        function showPageInfo() {
            const info = document.getElementById('pageInfo');
            info.innerHTML = `
                <p><strong>当前URL:</strong> ${window.location.href}</p>
                <p><strong>Origin:</strong> ${window.location.origin}</p>
                <p><strong>回调URL:</strong> ${window.location.origin}/auth-callback.html</p>
                <p><strong>Hash:</strong> ${window.location.hash || '(无)'}</p>
                <p><strong>Search:</strong> ${window.location.search || '(无)'}</p>
            `;
        }

        // 初始化QQ登录
        function initQQLogin() {
            log("开始初始化QQ登录按钮");
            try {
                const redirectUri = window.location.origin + "/auth-callback.html";
                log(`回调URL: ${redirectUri}`);
                
                QC.Login({
                    btnId: "qqLoginBtn",
                    size: "large",
                    scope: "get_user_info",
                    redirectURI: encodeURIComponent(redirectUri)
                });
                
                log("QQ登录按钮初始化成功");
                
                // 监听登录状态
                QC.Login.getMe(function(openId, accessToken) {
                    log(`QQ登录回调 - openId: ${openId}, accessToken: ${accessToken}`);
                    if (openId && accessToken) {
                        showStatus('success', '登录成功！');
                        getUserInfo(accessToken, openId);
                    }
                });
                
            } catch (error) {
                log(`初始化QQ登录失败: ${error.message}`);
                showStatus('error', `初始化失败: ${error.message}`);
            }
        }

        // 获取用户信息
        function getUserInfo(accessToken, openId) {
            log(`开始获取用户信息 - accessToken: ${accessToken}, openId: ${openId}`);
            
            QC.api("get_user_info", {
                accessToken: accessToken,
                openid: openId
            })
            .success(function(response) {
                log(`获取用户信息成功: ${JSON.stringify(response.data)}`);
                showStatus('success', `用户信息获取成功: ${response.data.nickname}`);
                
                // 存储到本地
                localStorage.setItem('qqUserInfo', JSON.stringify({
                    ...response.data,
                    accessToken: accessToken,
                    openId: openId
                }));
            })
            .error(function(error) {
                log(`获取用户信息失败: ${JSON.stringify(error)}`);
                showStatus('error', `获取用户信息失败: ${error.message || '未知错误'}`);
            });
        }

        // 检查登录状态
        function checkLoginStatus() {
            log("检查登录状态");
            
            const userInfo = localStorage.getItem('qqUserInfo');
            if (userInfo) {
                try {
                    const data = JSON.parse(userInfo);
                    log(`本地存储的用户信息: ${JSON.stringify(data)}`);
                    showStatus('info', `已登录用户: ${data.nickname || '未知'}`);
                } catch (e) {
                    log(`解析用户信息失败: ${e.message}`);
                    showStatus('error', '本地用户信息格式错误');
                }
            } else {
                log("本地无用户信息");
                showStatus('info', '未登录');
            }
        }

        // 清除本地存储
        function clearStorage() {
            localStorage.removeItem('qqUserInfo');
            localStorage.removeItem('returnUrl');
            log("已清除本地存储");
            showStatus('info', '本地存储已清除');
        }

        // 显示状态
        function showStatus(type, message) {
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log("页面加载完成");
            showPageInfo();
            
            // 延迟初始化QQ登录，确保SDK加载完成
            setTimeout(() => {
                if (typeof QC !== 'undefined') {
                    initQQLogin();
                } else {
                    log("QQ SDK未加载");
                    showStatus('error', 'QQ SDK未加载');
                }
            }, 1000);
            
            checkLoginStatus();
        });
    </script>
</body>
</html>
